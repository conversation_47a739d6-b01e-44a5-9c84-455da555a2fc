import { FontAwesome } from '@expo/vector-icons';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import { RefreshControl, ScrollView, Text, TouchableOpacity, View } from 'react-native';

import { LinkProps } from 'expo-router/build/link/Link';

interface MenuItemProps {
  icon: keyof typeof FontAwesome.glyphMap;
  title: string;
  href: LinkProps['href'];
  color?: string;
}

const MenuItem = ({ icon, title, href, color = '#3b82f6' }: MenuItemProps) => {
  return (
    <Link href={href} asChild>
      <TouchableOpacity className="flex-row items-center p-4 bg-white mb-2 rounded-xl">
        <View className="w-10 h-10 rounded-full bg-blue-50 items-center justify-center mr-4">
          <FontAwesome name={icon} size={20} color={color} />
        </View>
        <Text className="text-gray-800 font-medium text-lg">{title}</Text>
        <View className="flex-1 items-end">
          <FontAwesome name="chevron-right" size={16} color="#9CA3AF" />
        </View>
      </TouchableOpacity>
    </Link>
  );
};

export default function MoreScreen() {
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay for consistency
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-200">
        <Text className="text-xl font-bold text-gray-800">More Options</Text>
        <Text className="text-xs text-gray-500 mt-1">
          Access additional features and settings
        </Text>
      </View>

      {/* Menu Items */}
      <ScrollView
        className="flex-1 px-4 py-4"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#3B82F6']}
            tintColor="#3B82F6"
          />
        }
      >
        {/* <MenuItem
          icon="users"
          title="Teachers"
          href="/(admin)/_screens/teachers"
          color="#3B82F6"
        /> */}
        <MenuItem
          icon="money"
          title="Payments"
          href="/(admin)/_screens/payments"
          color="#10B981"
        />
        <MenuItem
          icon="bell"
          title="Notifications"
          href="/(admin)/_screens/notifications"
          color="#F59E0B"
        />

        <MenuItem
          icon="user-secret"
          title="User Management"
          href="/(admin)/_screens/user-management"
          color="#8B5CF6"
        />
        <MenuItem
          icon="cog"
          title="Settings"
          href="/(admin)/_screens/settings"
          color="#6B7280"
        />
      </ScrollView>
    </View>
  );
}
