import { FontAwesome } from '@expo/vector-icons';
import { collection, getDocs, orderBy, query } from 'firebase/firestore';
import { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Animated,
    Modal,
    ScrollView,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { Calendar, DateData } from 'react-native-calendars';
import { firestore } from '../../config/firebase';
import { useAuth } from '../../hooks/useAuth';

interface DateComponents {
  year: number;
  month: number;
  day: number;
}

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  dateComponents?: DateComponents;
  type: 'holiday' | 'exam' | 'activity' | 'other';
  targetAudience: 'all' | 'parents' | 'teachers' | 'specific_class';
  specificClass?: string;
}

export default function ParentCalendar() {
  // Get current user
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Animation for calendar
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  // Fetch events from Firestore
  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const eventsCollection = collection(firestore, 'events');
      const eventsQuery = query(eventsCollection, orderBy('date', 'asc'));
      const querySnapshot = await getDocs(eventsQuery);

      const eventsData: Event[] = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison
      const todayStr = today.toISOString().split('T')[0];

      console.log('Processing events from Firestore...');

      // Process events from Firestore
      querySnapshot.forEach((doc) => {
        const data = doc.data() as Omit<Event, 'id'>;
        // Make sure all required fields are present
        if (!data.title || !data.date || !data.type) {
          console.log(`Skipping event with missing required fields: ${doc.id}`);
          return;
        }

        // Handle different date formats (ISO string or Firestore timestamp)
        let eventDate: Date;
        if (typeof data.date === 'string') {
          eventDate = new Date(data.date);
        } else if (data.date && typeof data.date === 'object' && 'toDate' in data.date) {
          // Handle Firestore timestamp
          eventDate = (data.date as any).toDate();
        } else {
          console.log(`Skipping event with invalid date format: ${doc.id}`);
          return;
        }

        // Convert to Indian timezone (IST is UTC+5:30)
        // This ensures that dates are interpreted in Indian timezone
        const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
        const istDate = new Date(eventDate.getTime() + istOffset);

        // Format date as ISO string for consistent handling
        const eventDateStr = istDate.toISOString().split('T')[0];
        console.log(`Event date after IST adjustment: ${eventDateStr}`);

        // Also store the date components for easier comparison
        const eventDateComponents = {
          year: istDate.getFullYear(),
          month: istDate.getMonth() + 1, // getMonth() is 0-indexed
          day: istDate.getDate()
        };
        console.log(`Event date components: Year=${eventDateComponents.year}, Month=${eventDateComponents.month}, Day=${eventDateComponents.day}`);

        console.log(`Event: ${data.title}, Date: ${eventDateStr}, Type: ${data.type}, Audience: ${data.targetAudience || 'not specified'}`);

        // Skip past events but include today's events
        const eventDay = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());
        const todayDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

        console.log(`Event day: ${eventDay.toISOString()}, Today: ${todayDay.toISOString()}, Is past? ${eventDay < todayDay}`);

        // Skip if event date is before today
        if (eventDay < todayDay) {
          console.log(`Skipping past event: ${data.title}`);
          return;
        }

        // Include events that are for all or specifically for parents
        if (data.targetAudience === 'all' || data.targetAudience === 'parents' || !data.targetAudience) {
          console.log(`Adding event to list: ${data.title}`);
          eventsData.push({
            id: doc.id,
            ...data,
            date: eventDateStr, // Use the formatted date string
            dateComponents: eventDateComponents, // Add date components for easier comparison
            targetAudience: data.targetAudience || 'all', // Set default if not specified
            description: data.description || 'No description available', // Ensure description exists
            type: data.type || 'other' // Ensure type exists
          });
        } else {
          console.log(`Skipping event not for parents: ${data.title}`);
        }
      });

      console.log(`Found ${eventsData.length} events for parents`);
      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchEvents();
    } catch (error) {
      console.error('Error refreshing events:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Helper functions for event management
  function getEventTypeColor(type: string): string {
    switch (type) {
      case 'holiday':
        return '#65fe02';
      case 'exam':
        return '#fb0202';
      case 'activity':
        return '#10B981';
      default:
        return '#6B7280';
    }
  }

  const getMarkedDates = () => {
    const marked: any = {};

    if (events.length === 0) {
      // If no events, at least mark today's date
      const today = new Date().toISOString().split('T')[0];
      marked[today] = {
        selected: true,
        selectedColor: '#3b82f6',
      };
      return marked;
    }

    events.forEach((event) => {
      marked[event.date] = {
        marked: true,
        dotColor: getEventTypeColor(event.type),
        selected: event.date === selectedDate,
        selectedColor: event.date === selectedDate ? '#3B82F6' : 'rgba(59, 130, 246, 0.1)'
      };
    });

    return marked;
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'holiday':
        return 'calendar';
      case 'exam':
        return 'pencil';
      case 'activity':
        return 'star';
      default:
        return 'circle';
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Loading Overlay */}
      {loading && (
        <View className="absolute z-10 w-full h-full bg-black/30 items-center justify-center">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2 font-medium">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-200">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-xl sm:text-2xl font-bold text-gray-800">Calendar</Text>
            <Text className="text-xs sm:text-sm text-gray-500 mt-1">
              View school events and activities
            </Text>
          </View>
          <TouchableOpacity
            onPress={fetchEvents}
            disabled={loading}
            className="p-3 bg-blue-50 rounded-full"
          >
            <FontAwesome name="refresh" size={18} color={loading ? "#9CA3AF" : "#3B82F6"} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Calendar */}
      <Animated.View style={{ opacity: fadeAnim }} className="bg-white">
        <Calendar
          markedDates={getMarkedDates()}
          onDayPress={(day: DateData) => {
            setSelectedDate(day.dateString);
            const dayEvents = events.filter(e => e.date === day.dateString);
            if (dayEvents.length > 0) {
              setSelectedEvent(dayEvents[0]);
              setShowEventModal(true);
            }
          }}
          theme={{
            backgroundColor: '#ffffff',
            calendarBackground: '#ffffff',
            textSectionTitleColor: '#6B7280',
            selectedDayBackgroundColor: '#3B82F6',
            selectedDayTextColor: '#ffffff',
            todayTextColor: '#3B82F6',
            dayTextColor: '#1F2937',
            textDisabledColor: '#D1D5DB',
            dotColor: '#3B82F6',
            selectedDotColor: '#ffffff',
            arrowColor: '#3B82F6',
            monthTextColor: '#1F2937',
            textDayFontSize: 14,
            textMonthFontSize: 16,
            textDayHeaderFontSize: 12,
            'stylesheet.calendar.main': {
              week: {
                marginTop: 2,
                marginBottom: 2,
                flexDirection: 'row',
                justifyContent: 'space-around'
              }
            },
            'stylesheet.day.basic': {
              base: {
                width: 32,
                height: 32,
                alignItems: 'center',
                justifyContent: 'center'
              },
              dot: {
                width: 4,
                height: 4,
                marginTop: 1,
                borderRadius: 2
              }
            }
          }}
        />
      </Animated.View>

      {/* Event List */}
      <ScrollView
        className="flex-1 px-3 sm:px-4 py-4 sm:py-6"
        contentContainerStyle={{ paddingBottom: 150 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#3B82F6']}
            tintColor="#3B82F6"
          />
        }
      >
        {/* Today's Events Section */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-base sm:text-lg font-semibold text-gray-800">
              Today's Events
            </Text>
            <View className="bg-blue-100 px-3 py-1 rounded-full">
              <Text className="text-xs text-blue-600 font-medium">
                {new Date().toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}
              </Text>
            </View>
          </View>

          {/* Get today's date in the same format as our event dates */}
          {(() => {
            // Use Indian timezone (IST is UTC+5:30)
            const today = new Date();
            // Adjust for Indian timezone (UTC+5:30)
            const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
            const istDate = new Date(today.getTime() + istOffset);
            const todayStr = istDate.toISOString().split('T')[0]; // YYYY-MM-DD format
            console.log('Today string for comparison:', todayStr);
            console.log('Available event dates:', events.map(e => e.date).join(', '));

            // Check if we have any events for today
            // First, dump all events for debugging
            console.log('All events:');
            events.forEach((event, index) => {
              console.log(`Event ${index}: ${event.title}, Date: ${event.date}, Type: ${event.type}`);
              if (event.dateComponents) {
                console.log(`  Date components: Year=${event.dateComponents.year}, Month=${event.dateComponents.month}, Day=${event.dateComponents.day}`);
              }
            });

            // Get today's date in different formats for more flexible matching
            const todayDateObj = new Date(todayStr);
            const todayYear = todayDateObj.getFullYear();
            const todayMonth = todayDateObj.getMonth() + 1; // getMonth() is 0-indexed
            const todayDay = todayDateObj.getDate();

            console.log(`Today's date components: Year=${todayYear}, Month=${todayMonth}, Day=${todayDay}`);

            const todayEvents = events.filter(event => {
              console.log(`\nChecking if event is today: ${event.title}, date: ${event.date}`);

              // 1. Check using dateComponents (most reliable)
              if (event.dateComponents) {
                const componentsMatch = (
                  event.dateComponents.year === todayYear &&
                  event.dateComponents.month === todayMonth &&
                  event.dateComponents.day === todayDay
                );
                console.log(`Date components match: ${componentsMatch}`);
                if (componentsMatch) return true;
              }

              // 2. Direct string comparison
              const directMatch = event.date === todayStr;
              if (directMatch) {
                console.log(`Direct string match: ${directMatch}`);
                return true;
              }

              // 3. Try to extract just the date part if it's a full ISO string
              if (event.date && event.date.includes('T')) {
                const extractedDate = event.date.split('T')[0];
                const extractedMatch = extractedDate === todayStr;
                console.log(`Extracted date match: ${extractedMatch}`);
                if (extractedMatch) return true;
              }

              // 4. Compare date components from the date string
              try {
                const eventDate = new Date(event.date);
                const eventYear = eventDate.getFullYear();
                const eventMonth = eventDate.getMonth() + 1;
                const eventDay = eventDate.getDate();

                const dateComponentsMatch = (
                  eventYear === todayYear &&
                  eventMonth === todayMonth &&
                  eventDay === todayDay
                );

                console.log(`Event date components: Year=${eventYear}, Month=${eventMonth}, Day=${eventDay}`);
                console.log(`Date components match: ${dateComponentsMatch}`);

                if (dateComponentsMatch) return true;
              } catch (e) {
                console.log(`Error parsing date: ${e}`);
              }

              // 5. Check if the date string contains today's date (for format "2023-04-10")
              // This is a last resort and less reliable
              if (event.date) {
                // Format today's date as YYYY-MM-DD, MM/DD/YYYY, and DD-MM-YYYY for comparison
                const formats = [
                  `${todayYear}-${String(todayMonth).padStart(2, '0')}-${String(todayDay).padStart(2, '0')}`,
                  `${String(todayMonth).padStart(2, '0')}/${String(todayDay).padStart(2, '0')}/${todayYear}`,
                  `${String(todayDay).padStart(2, '0')}-${String(todayMonth).padStart(2, '0')}-${todayYear}`
                ];

                for (const format of formats) {
                  if (event.date.includes(format)) {
                    console.log(`Date string contains format ${format}`);
                    return true;
                  }
                }
              }

              // If we get here, this is not a today's event
              console.log(`Not a today's event`);
              return false;
            });

            console.log(`Found ${todayEvents.length} events for today`);
            return todayEvents.length === 0;
          })() ? (
            <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 items-center justify-center">
              <FontAwesome name="calendar-check-o" size={24} color="#9CA3AF" />
              <Text className="text-gray-500 mt-2 text-center">No events scheduled for today</Text>
            </View>
          ) : (
            <View className="space-y-3">
              {(() => {
                // Use Indian timezone (IST is UTC+5:30)
                const today = new Date();
                // Adjust for Indian timezone (UTC+5:30)
                const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
                const istDate = new Date(today.getTime() + istOffset);
                const todayStr = istDate.toISOString().split('T')[0]; // YYYY-MM-DD format
                // Get today's date in different formats for more flexible matching
                const todayDateObj = new Date(todayStr);
                const todayYear = todayDateObj.getFullYear();
                const todayMonth = todayDateObj.getMonth() + 1; // getMonth() is 0-indexed
                const todayDay = todayDateObj.getDate();

                console.log(`Today's date components for display: Year=${todayYear}, Month=${todayMonth}, Day=${todayDay}`);

                const todayEvents = events.filter(event => {
                  console.log(`Checking for display: ${event.title}, date: ${event.date}`);

                  // 1. Check using dateComponents (most reliable)
                  if (event.dateComponents) {
                    const componentsMatch = (
                      event.dateComponents.year === todayYear &&
                      event.dateComponents.month === todayMonth &&
                      event.dateComponents.day === todayDay
                    );
                    console.log(`Date components match: ${componentsMatch}`);
                    if (componentsMatch) return true;
                  }

                  // 2. Direct string comparison
                  const directMatch = event.date === todayStr;
                  if (directMatch) {
                    console.log(`Direct string match: ${directMatch}`);
                    return true;
                  }

                  // 3. Try to extract just the date part if it's a full ISO string
                  if (event.date && event.date.includes('T')) {
                    const extractedDate = event.date.split('T')[0];
                    const extractedMatch = extractedDate === todayStr;
                    console.log(`Extracted date match: ${extractedMatch}`);
                    if (extractedMatch) return true;
                  }

                  // 4. Compare date components from the date string
                  try {
                    const eventDate = new Date(event.date);
                    const eventYear = eventDate.getFullYear();
                    const eventMonth = eventDate.getMonth() + 1;
                    const eventDay = eventDate.getDate();

                    const dateComponentsMatch = (
                      eventYear === todayYear &&
                      eventMonth === todayMonth &&
                      eventDay === todayDay
                    );

                    console.log(`Event date components: Year=${eventYear}, Month=${eventMonth}, Day=${eventDay}`);
                    console.log(`Date components match: ${dateComponentsMatch}`);

                    if (dateComponentsMatch) return true;
                  } catch (e) {
                    console.log(`Error parsing date: ${e}`);
                  }

                  // 5. Check if the date string contains today's date (for format "2023-04-10")
                  // This is a last resort and less reliable
                  if (event.date) {
                    // Format today's date as YYYY-MM-DD, MM/DD/YYYY, and DD-MM-YYYY for comparison
                    const formats = [
                      `${todayYear}-${String(todayMonth).padStart(2, '0')}-${String(todayDay).padStart(2, '0')}`,
                      `${String(todayMonth).padStart(2, '0')}/${String(todayDay).padStart(2, '0')}/${todayYear}`,
                      `${String(todayDay).padStart(2, '0')}-${String(todayMonth).padStart(2, '0')}-${todayYear}`
                    ];

                    for (const format of formats) {
                      if (event.date.includes(format)) {
                        console.log(`Date string contains format ${format}`);
                        return true;
                      }
                    }
                  }

                  // If we get here, this is not a today's event
                  console.log(`Not a today's event for display`);
                  return false;
                });
                console.log(`Rendering ${todayEvents.length} events for today`);
                return todayEvents;
              })()
                .map((event) => (
                <TouchableOpacity
                  key={`today-${event.id}`}
                  className="bg-white rounded-xl p-3 shadow-sm border border-gray-100"
                  onPress={() => {
                    setSelectedEvent(event);
                    setShowEventModal(true);
                  }}
                  activeOpacity={0.7}
                  style={{
                    borderLeftWidth: 4,
                    borderLeftColor: getEventTypeColor(event.type)
                  }}
                >
                  <View className="flex-row items-center mb-2">
                    <View className={`w-8 h-8 rounded-full items-center justify-center mr-2`}
                      style={{ backgroundColor: `${getEventTypeColor(event.type)}20` }}>
                      <FontAwesome
                        name={getEventIcon(event.type)}
                        size={16}
                        color={getEventTypeColor(event.type)}
                      />
                    </View>
                    <Text className="text-base font-bold text-gray-800 flex-1 flex-wrap">
                      {event.title}
                    </Text>
                  </View>

                  <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                      <FontAwesome name="clock-o" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                      <Text className="text-sm text-gray-500">
                        Today
                      </Text>
                    </View>

                    <View className="flex-row space-x-1">
                      <View className="px-2 py-0.5 rounded-full" style={{ backgroundColor: `${getEventTypeColor(event.type)}20` }}>
                        <Text className="text-xs font-medium capitalize" style={{ color: getEventTypeColor(event.type) }}>
                          {event.type}
                        </Text>
                      </View>

                      {event.targetAudience && (
                        <View
                          className="px-2 py-0.5 rounded-full"
                          style={{
                            backgroundColor: event.targetAudience === 'parents' ? '#DBEAFE' :
                                            event.targetAudience === 'all' ? '#E0F2FE' : '#F3F4F6'
                          }}
                        >
                          <Text
                            className="text-xs font-medium capitalize"
                            style={{
                              color: event.targetAudience === 'parents' ? '#2563EB' :
                                     event.targetAudience === 'all' ? '#0284C7' : '#6B7280'
                            }}
                          >
                            {event.targetAudience === 'parents' ? 'For Parents' :
                             event.targetAudience === 'all' ? 'For Everyone' :
                             event.targetAudience === 'specific_class' ? 'For Class' : event.targetAudience}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Upcoming Events Section */}
        <View className="flex-row justify-between items-center mb-3">
          <Text className="text-base sm:text-lg font-semibold text-gray-800">
            Upcoming Events
          </Text>
          <TouchableOpacity
            onPress={fetchEvents}
            disabled={loading}
            className="p-2"
          >
            <FontAwesome name="refresh" size={16} color={loading ? "#9CA3AF" : "#3B82F6"} />
          </TouchableOpacity>
        </View>

        {(() => {
          // Use Indian timezone (IST is UTC+5:30)
          const today = new Date();
          // Adjust for Indian timezone (UTC+5:30)
          const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
          const istDate = new Date(today.getTime() + istOffset);
          const todayStr = istDate.toISOString().split('T')[0]; // YYYY-MM-DD format
          console.log('Upcoming events check - today:', todayStr);

          // Get today's date in different formats for more flexible matching
          const todayDateObj = new Date(todayStr);
          const todayYear = todayDateObj.getFullYear();
          const todayMonth = todayDateObj.getMonth() + 1; // getMonth() is 0-indexed
          const todayDay = todayDateObj.getDate();

          console.log(`Today's date components for upcoming: Year=${todayYear}, Month=${todayMonth}, Day=${todayDay}`);

          const upcomingEvents = events.filter(event => {
            console.log(`Checking if event is upcoming: ${event.title}, date: ${event.date}`);

            // Try different date formats for comparison
            // 1. Direct string comparison
            const directMatch = event.date === todayStr;

            // 2. Try to extract just the date part if it's a full ISO string
            let extractedDate = '';
            if (event.date && event.date.includes('T')) {
              extractedDate = event.date.split('T')[0];
            }

            // 3. Compare date components
            let dateComponentsMatch = false;
            try {
              const eventDate = new Date(event.date);
              const eventYear = eventDate.getFullYear();
              const eventMonth = eventDate.getMonth() + 1;
              const eventDay = eventDate.getDate();

              dateComponentsMatch = (eventYear === todayYear &&
                                    eventMonth === todayMonth &&
                                    eventDay === todayDay);

              console.log(`Event date components: Year=${eventYear}, Month=${eventMonth}, Day=${eventDay}`);
            } catch (e) {
              console.log(`Error parsing date: ${e}`);
            }

            // 1. Check if this is a today's event
            const isToday = directMatch ||
                           (extractedDate && extractedDate === todayStr) ||
                           dateComponentsMatch;

            if (isToday) {
              console.log(`Today's event - not showing in upcoming: ${event.title}`);
              return false;
            }

            // 2. Check if this is a past event
            try {
              // Use dateComponents if available (most reliable)
              if (event.dateComponents) {
                const isPast = (
                  event.dateComponents.year < todayYear ||
                  (event.dateComponents.year === todayYear && event.dateComponents.month < todayMonth) ||
                  (event.dateComponents.year === todayYear && event.dateComponents.month === todayMonth && event.dateComponents.day < todayDay)
                );

                if (isPast) {
                  console.log(`Past event based on dateComponents: ${event.title}`);
                  return false;
                }
              }

              // Otherwise use the date string
              const eventDate = new Date(event.date);
              const eventYear = eventDate.getFullYear();
              const eventMonth = eventDate.getMonth() + 1;
              const eventDay = eventDate.getDate();

              const isPast = (
                eventYear < todayYear ||
                (eventYear === todayYear && eventMonth < todayMonth) ||
                (eventYear === todayYear && eventMonth === todayMonth && eventDay < todayDay)
              );

              if (isPast) {
                console.log(`Past event: ${event.title}`);
                return false;
              }
            } catch (e) {
              console.log(`Error checking if past event: ${e}`);
            }

            // If we get here, this is an upcoming event
            console.log(`Upcoming event: ${event.title}`);
            return true;
          });
          console.log(`Found ${upcomingEvents.length} upcoming events`);
          return upcomingEvents.length === 0 && !loading;
        })() ? (
          <View className="items-center justify-center py-8">
            <FontAwesome name="calendar-o" size={50} color="#D1D5DB" />
            <Text className="text-gray-400 mt-4 text-center">
              No upcoming events found.
            </Text>
            <TouchableOpacity
              onPress={fetchEvents}
              className="mt-4 bg-blue-100 px-4 py-2 rounded-lg"
            >
              <Text className="text-blue-600 font-medium">Refresh</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View className="space-y-4">
            {(() => {
              // Use Indian timezone (IST is UTC+5:30)
              const today = new Date();
              // Adjust for Indian timezone (UTC+5:30)
              const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
              const istDate = new Date(today.getTime() + istOffset);
              const todayStr = istDate.toISOString().split('T')[0]; // YYYY-MM-DD format

              // Get today's date in different formats for more flexible matching
              const todayDateObj = new Date(todayStr);
              const todayYear = todayDateObj.getFullYear();
              const todayMonth = todayDateObj.getMonth() + 1; // getMonth() is 0-indexed
              const todayDay = todayDateObj.getDate();

              console.log(`Today's date components for display upcoming: Year=${todayYear}, Month=${todayMonth}, Day=${todayDay}`);

              const upcomingEvents = events.filter(event => {
                console.log(`Filtering upcoming event for display: ${event.title}, date: ${event.date}`);

                // 1. Check if this is a today's event using dateComponents (most reliable)
                if (event.dateComponents) {
                  const isToday = (
                    event.dateComponents.year === todayYear &&
                    event.dateComponents.month === todayMonth &&
                    event.dateComponents.day === todayDay
                  );
                  if (isToday) {
                    console.log(`Today's event based on dateComponents - not showing in upcoming display`);
                    return false;
                  }

                  // Also check if this is a past event
                  const isPast = (
                    event.dateComponents.year < todayYear ||
                    (event.dateComponents.year === todayYear && event.dateComponents.month < todayMonth) ||
                    (event.dateComponents.year === todayYear && event.dateComponents.month === todayMonth && event.dateComponents.day < todayDay)
                  );

                  if (isPast) {
                    console.log(`Past event based on dateComponents - not showing in upcoming display`);
                    return false;
                  }
                }

                // 2. Direct string comparison for today's events
                if (event.date === todayStr) {
                  console.log(`Today's event based on direct match - not showing in upcoming display`);
                  return false;
                }

                // 3. Try to extract just the date part if it's a full ISO string
                if (event.date && event.date.includes('T')) {
                  const extractedDate = event.date.split('T')[0];
                  if (extractedDate === todayStr) {
                    console.log(`Today's event based on extracted date - not showing in upcoming display`);
                    return false;
                  }
                }

                // 4. Compare date components from the date string
                try {
                  const eventDate = new Date(event.date);
                  const eventYear = eventDate.getFullYear();
                  const eventMonth = eventDate.getMonth() + 1;
                  const eventDay = eventDate.getDate();

                  // Check if this is a today's event
                  const isToday = (
                    eventYear === todayYear &&
                    eventMonth === todayMonth &&
                    eventDay === todayDay
                  );

                  if (isToday) {
                    console.log(`Today's event based on date components - not showing in upcoming display`);
                    return false;
                  }

                  // Also check if this is a past event
                  const isPast = (
                    eventYear < todayYear ||
                    (eventYear === todayYear && eventMonth < todayMonth) ||
                    (eventYear === todayYear && eventMonth === todayMonth && eventDay < todayDay)
                  );

                  if (isPast) {
                    console.log(`Past event - not showing in upcoming display`);
                    return false;
                  }
                } catch (e) {
                  console.log(`Error checking event date: ${e}`);
                }

                // If we get here, this is an upcoming event
                console.log(`Upcoming event for display: ${event.title}`);
                return true;
              });
              console.log(`Displaying ${upcomingEvents.length} upcoming events`);
              return upcomingEvents;
            })()
              .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
              .map((event) => (
              <TouchableOpacity
                key={event.id}
                className="bg-white rounded-xl p-3 shadow-sm border border-gray-100"
                onPress={() => {
                  setSelectedEvent(event);
                  setShowEventModal(true);
                }}
                activeOpacity={0.7}
                style={{
                  borderLeftWidth: 4,
                  borderLeftColor: getEventTypeColor(event.type)
                }}
              >
                <View className="flex-row items-center mb-2">
                  <View className={`w-8 h-8 rounded-full items-center justify-center mr-2`}
                    style={{ backgroundColor: `${getEventTypeColor(event.type)}20` }}>
                    <FontAwesome
                      name={getEventIcon(event.type)}
                      size={16}
                      color={getEventTypeColor(event.type)}
                    />
                  </View>
                  <Text className="text-base font-bold text-gray-800 flex-1 flex-wrap">
                    {event.title}
                  </Text>
                </View>

                <View className="flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <FontAwesome name="calendar" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                    <Text className="text-sm text-gray-500">
                      {new Date(event.date).toLocaleDateString('en-IN', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </Text>
                  </View>

                  <View className="flex-row space-x-1">
                    <View className="px-2 py-0.5 rounded-full" style={{ backgroundColor: `${getEventTypeColor(event.type)}20` }}>
                      <Text className="text-xs font-medium capitalize" style={{ color: getEventTypeColor(event.type) }}>
                        {event.type}
                      </Text>
                    </View>

                    {event.targetAudience && (
                      <View
                        className="px-2 py-0.5 rounded-full"
                        style={{
                          backgroundColor: event.targetAudience === 'parents' ? '#DBEAFE' :
                                          event.targetAudience === 'all' ? '#E0F2FE' : '#F3F4F6'
                        }}
                      >
                        <Text
                          className="text-xs font-medium capitalize"
                          style={{
                            color: event.targetAudience === 'parents' ? '#2563EB' :
                                   event.targetAudience === 'all' ? '#0284C7' : '#6B7280'
                          }}
                        >
                          {event.targetAudience === 'parents' ? 'For Parents' :
                           event.targetAudience === 'all' ? 'For Everyone' :
                           event.targetAudience === 'specific_class' ? 'For Class' : event.targetAudience}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Event Details Modal */}
      <Modal
        visible={showEventModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowEventModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl max-h-[90%] overflow-y-auto">
            {/* Close button at top right */}
            <TouchableOpacity
              onPress={() => setShowEventModal(false)}
              className="absolute right-4 top-4 z-10 bg-gray-100 p-2 rounded-full"
            >
              <FontAwesome name="close" size={20} color="#4B5563" />
            </TouchableOpacity>

            {selectedEvent && (
              <View>
                {/* Colored header based on event type */}
                <View
                  className="p-6 pt-12 rounded-t-3xl"
                  style={{ backgroundColor: `${getEventTypeColor(selectedEvent.type)}15` }}
                >
                  {/* Event type badge */}
                  <View
                    className="self-start px-4 py-2 rounded-full mb-3 flex-row items-center"
                    style={{ backgroundColor: `${getEventTypeColor(selectedEvent.type)}30` }}
                  >
                    <FontAwesome
                      name={getEventIcon(selectedEvent.type)}
                      size={14}
                      color={getEventTypeColor(selectedEvent.type)}
                      style={{ marginRight: 6 }}
                    />
                    <Text
                      className="text-sm font-medium capitalize"
                      style={{ color: getEventTypeColor(selectedEvent.type) }}
                    >
                      {selectedEvent.type}
                    </Text>
                  </View>

                  {/* Event title */}
                  <Text className="text-2xl font-bold text-gray-800 mb-1">
                    {selectedEvent.title}
                  </Text>

                  {/* Date with icon */}
                  <View className="flex-row items-center mt-2">
                    <FontAwesome name="calendar" size={14} color="#6B7280" style={{ marginRight: 8 }} />
                    <Text className="text-base text-gray-600">
                      {new Date(selectedEvent.date).toLocaleDateString('en-IN', {
                        weekday: 'long',
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                      })}
                    </Text>
                  </View>
                </View>

                {/* Event details content */}
                <View className="p-6">
                  {/* Description section */}
                  <View className="mb-6">
                    <Text className="text-base font-semibold text-gray-700 mb-2">
                      Description
                    </Text>
                    <View className="bg-gray-50 p-4 rounded-xl">
                      <Text className="text-gray-800 leading-relaxed">
                        {selectedEvent.description || 'No description available'}
                      </Text>
                    </View>
                  </View>

                  {/* For whom section */}
                  <View className="mb-6">
                    <Text className="text-base font-semibold text-gray-700 mb-2">
                      Audience
                    </Text>
                    <View className="flex-row items-center bg-blue-50 p-3 rounded-xl">
                      <FontAwesome name="users" size={16} color="#3B82F6" style={{ marginRight: 10 }} />
                      <Text className="text-blue-700 capitalize">
                        {selectedEvent.targetAudience === 'specific_class'
                          ? `Class ${selectedEvent.specificClass}`
                          : selectedEvent.targetAudience === 'all'
                            ? 'For Everyone'
                            : selectedEvent.targetAudience}
                      </Text>
                    </View>
                  </View>

                  {/* Close button at bottom */}
                  <TouchableOpacity
                    className="bg-gray-100 py-3 rounded-xl mt-4"
                    onPress={() => setShowEventModal(false)}
                  >
                    <Text className="text-gray-700 font-medium text-center">Close</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}
