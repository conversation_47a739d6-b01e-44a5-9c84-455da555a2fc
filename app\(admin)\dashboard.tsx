import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { collection, getCountFromServer, getDocs, query, where } from 'firebase/firestore';
import { useEffect, useState } from 'react';
import { ActivityIndicator, RefreshControl, ScrollView, Text, TouchableOpacity, useWindowDimensions, View } from 'react-native';
import { firestore } from '../../config/firebase';
import { useAuth } from '../../hooks/useAuth';

interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  todayAttendance: {
    total: number;
    present: number;
  };
  pendingPayments: number;
  totalRevenue: number;
  recentNotifications: Array<{
    id: string;
    title: string;
    message: string;
    date: string;
  }>;
}

export default function AdminDashboard() {
  // We need auth context but don't directly use the user object
  useAuth();
  const router = useRouter();
  const { width } = useWindowDimensions();
  const isMobile = width < 768;
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    totalTeachers: 0,
    todayAttendance: {
      total: 0,
      present: 0,
    },
    pendingPayments: 0,
    totalRevenue: 0,
    recentNotifications: [],
  });

  // Fetch dashboard data from Firestore
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      console.log('Fetching dashboard data from Firestore...');

      // Fetch total students count
      const studentsCollection = collection(firestore, 'students');
      const studentsQuery = query(studentsCollection, where('status', '==', 'active'));
      const studentsSnapshot = await getCountFromServer(studentsQuery);
      const totalStudents = studentsSnapshot.data().count;
      console.log('Total active students:', totalStudents);

      // Fetch total teachers count
      const teachersCollection = collection(firestore, 'teachers');
      const teachersSnapshot = await getCountFromServer(teachersCollection);
      const totalTeachers = teachersSnapshot.data().count;
      console.log('Total teachers:', totalTeachers);

      // Fetch pending payments count
      const paymentsCollection = collection(firestore, 'payments');
      const pendingPaymentsQuery = query(paymentsCollection, where('status', '==', 'pending'));
      const pendingPaymentsSnapshot = await getCountFromServer(pendingPaymentsQuery);
      const pendingPayments = pendingPaymentsSnapshot.data().count;
      console.log('Pending payments:', pendingPayments);

      // Fetch recent notifications
      const notificationsCollection = collection(firestore, 'notifications');
      const notificationsQuery = query(notificationsCollection, where('audience', 'in', ['all', 'admin']));
      const notificationsSnapshot = await getDocs(notificationsQuery);
      const recentNotifications: Array<{
        id: string;
        title: string;
        message: string;
        date: string;
      }> = [];

      notificationsSnapshot.forEach((doc) => {
        const data = doc.data();
        recentNotifications.push({
          id: doc.id,
          title: data.title || 'Notification',
          message: data.message || 'No message',
          date: data.date || new Date().toISOString().split('T')[0],
        });
      });

      // Calculate today's attendance (this is more complex and might need a different approach)
      // For now, we'll use the student count as the total and estimate present students
      const todayAttendance = {
        total: totalStudents,
        present: Math.round(totalStudents * 0.95), // Assuming 95% attendance as a placeholder
      };

      // Update the stats state with real data
      setStats({
        totalStudents,
        totalTeachers,
        todayAttendance,
        pendingPayments,
        totalRevenue: stats.totalRevenue, // Keep the existing value for now
        recentNotifications: recentNotifications.length > 0 ? recentNotifications : stats.recentNotifications,
      });

      console.log('Dashboard data fetched successfully');
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchDashboardData();
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const StatCard = ({
    title,
    value,
    icon,
    color,
  }: {
    title: string;
    value: string | number;
    icon: keyof typeof FontAwesome.glyphMap;
    color: string;
  }) => (
    <View
      className={`${isMobile ? 'w-[48%]' : 'flex-1'} ${isMobile ? 'p-3' : 'p-4'} rounded-xl ${isMobile ? 'mb-2 mx-[1%]' : 'mx-1 mb-2'} shadow-sm`}
      style={{ backgroundColor: color, elevation: 2 }}
    >
      <View className="flex-row justify-between items-center mb-1">
        <View className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} rounded-full bg-white/20 items-center justify-center`}>
          <FontAwesome name={icon} size={isMobile ? 16 : 20} color="white" />
        </View>
        <Text className={`text-white ${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{value}</Text>
      </View>
      <Text className="text-white/90 text-xs">{title}</Text>
    </View>
  );

  const QuickAction = ({
    title,
    icon,
    onPress,
  }: {
    title: string;
    icon: keyof typeof FontAwesome.glyphMap;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      className={`${isMobile ? 'w-[48%]' : 'w-[23%]'} ${isMobile ? 'p-3' : 'p-4'} bg-white rounded-xl ${isMobile ? 'mb-2 mx-[1%]' : 'mb-3 mx-1'} items-center shadow-sm`}
      style={{ elevation: 2 }}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} rounded-full bg-blue-500 items-center justify-center mb-2`}>
        <FontAwesome name={icon} size={isMobile ? 18 : 20} color="white" />
      </View>
      <Text className="text-gray-700 font-medium text-center text-xs">{title}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView
      className="flex-1 bg-gray-50 px-4 pt-3 pb-36"
      contentContainerStyle={{ paddingBottom: 120 }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={['#3B82F6']}
          tintColor="#3B82F6"
        />
      }
    >
      <View className={`${isMobile ? 'flex-row' : 'flex-row'} justify-between items-${isMobile ? 'start' : 'center'} mb-6 bg-white p-4 rounded-xl shadow-sm`} style={{ elevation: 1 }}>
        <View>
          <Text className="text-2xl font-bold text-gray-800">Dashboard</Text>
          {/* <Text className="text-blue-500 font-medium">
            Welcome back, Admin
          </Text> */}
        </View>
        <View className={`bg-gray-100 px-3 py-2 rounded-lg ${isMobile ? 'mt-0 self-start' : ''}`}>
          <Text className="text-gray-600 font-medium">
            {new Date().toLocaleDateString('en-IN', {
              weekday: isMobile ? 'short' : 'long',
              year: 'numeric',
              month: isMobile ? 'short' : 'long',
              day: 'numeric',
            })}
          </Text>
        </View>
      </View>

      {/* Stats Grid */}
      <View className="flex-row flex-wrap mb-4">
        {loading ? (
          // Loading state
          <View className="w-full flex-row flex-wrap justify-center py-8">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="w-full text-center text-gray-500 mt-4">Loading dashboard data...</Text>
          </View>
        ) : (
          // Data loaded state
          <>
            <StatCard
              title="Total Students"
              value={stats.totalStudents}
              icon="graduation-cap"
              color="#3B82F6"
            />
            <StatCard
              title="Total Teachers"
              value={stats.totalTeachers}
              icon="users"
              color="#10B981"
            />
            <StatCard
              title="Today's Attendance"
              value={stats.todayAttendance.total > 0
                ? `${Math.round((stats.todayAttendance.present / stats.todayAttendance.total) * 100)}%`
                : '0%'}
              icon="calendar-check-o"
              color="#F59E0B"
            />
            <StatCard
              title="Pending Payments"
              value={stats.pendingPayments}
              icon="money"
              color="#EF4444"
            />
          </>
        )}
      </View>

      {/* Quick Actions */}
      <View className="flex-row items-center mb-4">
        <View className="w-1 h-6 bg-blue-500 rounded-full mr-2"></View>
        <Text className="text-lg font-semibold text-gray-800">Quick Actions</Text>
      </View>
      <View className="flex-row flex-wrap mb-6 justify-between">
        <QuickAction
          title="Add Student"
          icon="user-plus"
          onPress={() => {
            router.push('/students');
          }}
        />
        <QuickAction
          title="Add Teacher"
          icon="user-plus"
          onPress={() => {
            router.push('/(admin)/_screens/user-management');
          }}
        />
        <QuickAction
          title="Send Notice"
          icon="bell"
          onPress={() => {
            router.push('/(admin)/_screens/notifications');
          }}
        />
        <QuickAction
          title="View Payments"
          icon="credit-card"
          onPress={() => {
            router.push('/(admin)/_screens/payments');
          }}
        />
      </View>

      {/* Revenue Chart */}
      {/* <View
        className="mb-6 p-6 rounded-xl shadow-md"
        style={{
          backgroundColor: '#22c55e', // Solid green color for consistent display
          elevation: 4,
          shadowColor: '#22c55e',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 4,
        }}>
        <Text className="text-lg font-semibold mb-2 text-white">Revenue Overview</Text>
        {loading ? (
          <View className="py-2">
            <ActivityIndicator color="white" />
            <Text className="text-white/80 mt-2">Loading revenue data...</Text>
          </View>
        ) : (
          <>
            <Text className={`${isMobile ? 'text-3xl' : 'text-4xl'} font-bold text-white`}>
              ₹{stats.totalRevenue.toLocaleString()}
            </Text>
            <Text className="text-white/80">Total Revenue This Month</Text>
          </>
        )}
      </View> */}

      {/* Recent Notifications */}
      <View>
        <View className="flex-row items-center mb-4">
          <View className="w-1 h-6 bg-blue-500 rounded-full mr-2"></View>
          <Text className="text-lg font-semibold text-gray-800">Recent Notifications</Text>
        </View>

        {loading ? (
          // Loading state
          <View className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 items-center justify-center">
            <ActivityIndicator size="small" color="#3B82F6" />
            <Text className="text-gray-500 mt-2">Loading notifications...</Text>
          </View>
        ) : stats.recentNotifications.length === 0 ? (
          // No notifications state
          <View className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 items-center justify-center">
            <FontAwesome name="bell-slash" size={24} color="#9CA3AF" />
            <Text className="text-gray-500 mt-2">No recent notifications</Text>
          </View>
        ) : (
          // Notifications list
          stats.recentNotifications.map((notification) => (
            <View
              key={notification.id}
              className="mb-4 p-4 bg-white rounded-xl border-l-4 border-blue-500 shadow-sm"
              style={{ elevation: 1 }}
            >
              <View className="flex-row justify-between items-center mb-2">
                <View className="flex-row items-center">
                  <View className="w-8 h-8 rounded-full bg-blue-100 items-center justify-center mr-2">
                    <FontAwesome
                      name={notification.title.includes('Payment') ? 'credit-card' : 'bell'}
                      size={16}
                      color="#3b82f6"
                    />
                  </View>
                  <Text className="font-medium text-gray-800">{notification.title}</Text>
                </View>
                <Text className="text-gray-500 text-xs">{notification.date}</Text>
              </View>
              <Text className="text-gray-600 ml-10">{notification.message}</Text>
            </View>
          ))
        )}
      </View>
    </ScrollView>
  );
}