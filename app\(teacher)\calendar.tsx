import { FontAwesome } from '@expo/vector-icons';
import { collection, getDocs, orderBy, query } from 'firebase/firestore';
import { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Animated,
    Modal,
    ScrollView,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { Calendar, DateData } from 'react-native-calendars';
import { firestore } from '../../config/firebase';
import { useAuth } from '../../hooks/useAuth';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  type: 'holiday' | 'exam' | 'activity' | 'other';
  targetAudience: 'all' | 'parents' | 'teachers' | 'specific_class';
  specificClass?: string;
}

export default function TeacherCalendar() {
  // Get current user
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Animation for calendar
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  // Fetch events from Firestore
  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const eventsCollection = collection(firestore, 'events');
      const eventsQuery = query(eventsCollection, orderBy('date', 'asc'));
      const querySnapshot = await getDocs(eventsQuery);

      const eventsData: Event[] = [];

      // Get current date in local timezone (device timezone)
      const today = new Date();
      
      // Get local date components (already in device timezone)
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth() + 1; // getMonth() is 0-indexed
      const todayDay = today.getDate();
      
      // Create a clean date string in YYYY-MM-DD format for comparison
      const todayStr = `${todayYear}-${String(todayMonth).padStart(2, '0')}-${String(todayDay).padStart(2, '0')}`;
      
      // Create date objects for comparison (set to start of day)
      const todayDate = new Date(todayYear, todayMonth - 1, todayDay);
      
      console.log(`Today's date (local): ${todayStr}`);

      console.log(`Today's date for teacher calendar: ${todayStr} (${todayYear}-${todayMonth}-${todayDay})`);
      console.log('Processing events from Firestore for teacher calendar...');

      // Process events from Firestore
      querySnapshot.forEach((doc) => {
        const data = doc.data() as Omit<Event, 'id'>;
        // Make sure all required fields are present
        if (!data.title || !data.date || !data.type) {
          console.log(`Skipping event with missing required fields: ${doc.id}`);
          return;
        }

        // Parse the event date
        let eventDate: Date;
        if (data.date && typeof data.date === 'object' && 'toDate' in data.date) {
          // Handle Firestore timestamp
          eventDate = (data.date as any).toDate();
        } else if (typeof data.date === 'string') {
          // Handle ISO string or other string format
          eventDate = new Date(data.date);
          
          // If the date is invalid, try parsing as DD/MM/YYYY format
          if (isNaN(eventDate.getTime())) {
            const parts = data.date.split('/');
            if (parts.length === 3) {
              eventDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
            }
          }
        } else {
          console.log(`Skipping event with invalid date format: ${doc.id}`, data.date);
          return;
        }

        // If we still don't have a valid date, skip this event
        if (isNaN(eventDate.getTime())) {
          console.log(`Skipping event with invalid date: ${doc.id}`, data.date);
          return;
        }

        // Get local date components (in device timezone)
        const eventYear = eventDate.getFullYear();
        const eventMonth = eventDate.getMonth() + 1;
        const eventDay = eventDate.getDate();
        
        // Format date as YYYY-MM-DD for consistent comparison
        const eventDateStr = `${eventYear}-${String(eventMonth).padStart(2, '0')}-${String(eventDay).padStart(2, '0')}`;

        console.log(`Event: ${data.title}, Date: ${eventDateStr}, Components: ${eventYear}-${eventMonth}-${eventDay}, Type: ${data.type}, Audience: ${data.targetAudience || 'not specified'}`);

        // Create date objects for comparison
        const eventDateObj = new Date(eventYear, eventMonth - 1, eventDay);
        
        // Skip past events (before today)
        const isPast = eventDateObj < todayDate;
        const isToday = (
          eventYear === todayYear &&
          eventMonth === todayMonth &&
          eventDay === todayDay
        );

        console.log(`Event date: ${eventDateStr}, isToday: ${isToday}, isPast: ${isPast}`);

        // Skip if event date is before today
        if (isPast && !isToday) {
          console.log(`Skipping past event: ${data.title}`);
          return;
        }

        // Include events that are for all or specifically for teachers
        if (data.targetAudience === 'all' || data.targetAudience === 'teachers' || !data.targetAudience) {
          console.log(`Adding event to list: ${data.title}`);
          eventsData.push({
            id: doc.id,
            ...data,
            date: eventDateStr, // Use the formatted date string
            targetAudience: data.targetAudience || 'all', // Set default if not specified
            description: data.description || 'No description available', // Ensure description exists
            type: data.type || 'other' // Ensure type exists
          });
        } else {
          console.log(`Skipping event not for teachers: ${data.title}`);
        }
      });

      console.log(`Found ${eventsData.length} events for teachers`);
      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchEvents();
    } catch (error) {
      console.error('Error refreshing events:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'holiday':
        return '#FCD34D'; // yellow
      case 'exam':
        return '#F87171'; // red
      case 'activity':
        return '#60A5FA'; // blue
      default:
        return '#A3A3A3'; // gray
    }
  };

  const getMarkedDates = () => {
    const marked: any = {};

    if (events.length === 0) {
      // If no events, at least mark today's date
      const today = new Date().toISOString().split('T')[0];
      marked[today] = {
        selected: true,
        selectedColor: '#3b82f6',
      };
      return marked;
    }

    events.forEach((event) => {
      marked[event.date] = {
        marked: true,
        dotColor: getEventTypeColor(event.type),
        selected: event.date === selectedDate,
        selectedColor: event.date === selectedDate ? '#3B82F6' : 'rgba(59, 130, 246, 0.1)'
      };
    });

    return marked;
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'holiday':
        return 'calendar';
      case 'exam':
        return 'pencil';
      case 'activity':
        return 'star';
      default:
        return 'circle';
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Loading Overlay */}
      {loading && (
        <View className="absolute z-10 w-full h-full bg-black/30 items-center justify-center">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2 font-medium">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-200">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-xl sm:text-2xl font-bold text-gray-800">Calendar</Text>
            <Text className="text-xs sm:text-sm text-gray-500 mt-1">
              View school events and activities
            </Text>
          </View>
          <View className="h-10 w-10 bg-blue-100 rounded-full items-center justify-center">
            <FontAwesome name="graduation-cap" size={20} color="#3B82F6" />
          </View>
        </View>
      </View>

      {/* Calendar */}
      <Animated.View style={{ opacity: fadeAnim }} className="bg-white">
        <Calendar
          markedDates={getMarkedDates()}
          onDayPress={(day: DateData) => {
            setSelectedDate(day.dateString);
            const dayEvents = events.filter(e => e.date === day.dateString);
            if (dayEvents.length > 0) {
              setSelectedEvent(dayEvents[0]);
              setShowEventModal(true);
            }
          }}
          theme={{
            backgroundColor: '#ffffff',
            calendarBackground: '#ffffff',
            textSectionTitleColor: '#6B7280',
            selectedDayBackgroundColor: '#3B82F6',
            selectedDayTextColor: '#ffffff',
            todayTextColor: '#3B82F6',
            dayTextColor: '#1F2937',
            textDisabledColor: '#D1D5DB',
            dotColor: '#3B82F6',
            selectedDotColor: '#ffffff',
            arrowColor: '#3B82F6',
            monthTextColor: '#1F2937',
            textDayFontSize: 14,
            textMonthFontSize: 16,
            textDayHeaderFontSize: 12
          }}
        />
      </Animated.View>

      {/* Event List */}
      <ScrollView
        className="flex-1 px-3 sm:px-4 py-4 sm:py-6"
        contentContainerStyle={{ paddingBottom: 150 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#3B82F6']}
            tintColor="#3B82F6"
          />
        }
      >
        {/* Today's Events Section */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-base sm:text-lg font-semibold text-gray-800">
              Today's Events
            </Text>
            <View className="bg-blue-100 px-3 py-1 rounded-full">
              <Text className="text-xs text-blue-600 font-medium">
                {new Date().toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}
              </Text>
            </View>
          </View>

          {/* Get today's date in the same format as our event dates */}
          {(() => {
            // Use Indian timezone (IST is UTC+5:30)
            const today = new Date();
            const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
            const istDate = new Date(today.getTime() + istOffset);
            const todayStr = istDate.toISOString().split('T')[0]; // YYYY-MM-DD format
            const todayYear = istDate.getFullYear();
            const todayMonth = istDate.getMonth() + 1; // getMonth() is 0-indexed
            const todayDay = istDate.getDate();

            console.log(`Teacher calendar - Today's date: ${todayStr} (${todayYear}-${todayMonth}-${todayDay})`);
            console.log('Teacher calendar - Available event dates:', events.map(e => e.date).join(', '));

            // Get today's events by comparing date strings directly
            const todayEvents = events.filter(event => {
              try {
                // Parse the stored date string (should be in YYYY-MM-DD format)
                const [year, month, day] = event.date.split('-').map(Number);
                return (
                  year === todayYear &&
                  month === todayMonth &&
                  day === todayDay
                );
              } catch (e) {
                console.error('Error parsing event date:', e);
                return false;
              }
            });

            console.log(`Teacher calendar - Found ${todayEvents.length} events for today`);
            return todayEvents.length === 0;
          })() ? (
            <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 items-center justify-center">
              <FontAwesome name="calendar-check-o" size={24} color="#9CA3AF" />
              <Text className="text-gray-500 mt-2 text-center">No events scheduled for today</Text>
            </View>
          ) : (
            <View className="space-y-3">
              {(() => {
                // Get today's date components once
                const today = new Date();
                const todayYear = today.getFullYear();
                const todayMonth = today.getMonth() + 1;
                const todayDay = today.getDate();
                const todayStr = `${todayYear}-${String(todayMonth).padStart(2, '0')}-${String(todayDay).padStart(2, '0')}`;

                console.log(`Teacher calendar - Today's date for display: ${todayStr}`);

                // Filter today's events
                const todayEvents = events.filter(event => {
                  try {
                    const [year, month, day] = event.date.split('-').map(Number);
                    return (
                      year === todayYear &&
                      month === todayMonth &&
                      day === todayDay
                    );
                  } catch (e) {
                    console.error('Error parsing event date:', e);
                    return false;
                  }

                  // Additional check for ISO string format (should be handled by the try block above)
                  if (event.date && event.date.includes('T')) {
                    try {
                      const extractedDate = event.date.split('T')[0];
                      const [year, month, day] = extractedDate.split('-').map(Number);
                      return (
                        year === todayYear &&
                        month === todayMonth &&
                        day === todayDay
                      );
                    } catch (e) {
                      console.error('Error parsing ISO date:', e);
                    }
                  }
                  return false;
                });
                console.log(`Teacher calendar - Rendering ${todayEvents.length} events for today`);
                return todayEvents;
              })()
                .map((event) => (
                <TouchableOpacity
                  key={`today-${event.id}`}
                  className="bg-white rounded-xl p-3 shadow-sm border border-gray-100"
                  onPress={() => {
                    setSelectedEvent(event);
                    setShowEventModal(true);
                  }}
                  activeOpacity={0.7}
                  style={{
                    borderLeftWidth: 4,
                    borderLeftColor: getEventTypeColor(event.type)
                  }}
                >
                  <View className="flex-row justify-between items-start mb-2">
                    <View className="flex-1 pr-2">
                      <Text className="font-semibold text-gray-900 text-base">
                        {event.title}
                      </Text>
                      <View className="mt-1">
                        <View className="flex-row items-center">
                          <FontAwesome name="calendar" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                          <Text className="text-sm text-gray-500">
                            {(() => {
                              try {
                                const [year, month, day] = event.date.split('-').map(Number);
                                const date = new Date(year, month - 1, day);
                                return date.toLocaleDateString('en-IN', {
                                  weekday: 'short',
                                  day: 'numeric',
                                  month: 'short',
                                  year: 'numeric'
                                });
                              } catch (e) {
                                console.error('Error formatting date:', e);
                                return 'Invalid date';
                              }
                            })()}
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View
                      className="px-2.5 py-1 rounded-full"
                      style={{
                        backgroundColor: `${getEventTypeColor(event.type)}10`,
                        borderWidth: 1,
                        borderColor: getEventTypeColor(event.type)
                      }}
                    >
                      <Text
                        className="text-xs font-medium capitalize"
                        style={{ color: getEventTypeColor(event.type) }}
                      >
                        {event.type}
                      </Text>
                    </View>
                  </View>

                  {event.description && (
                    <Text className="text-gray-600 text-sm mb-3" numberOfLines={2}>
                      {event.description}
                    </Text>
                  )}

                  <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                      <FontAwesome name="clock-o" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                      <Text className="text-sm text-gray-500">
                        Today
                      </Text>
                    </View>
                    {event.targetAudience && (
                      <View
                        className="px-2 py-0.5 rounded-full"
                        style={{
                          backgroundColor: event.targetAudience === 'teachers' ? '#DBEAFE' :
                                          event.targetAudience === 'all' ? '#E0F2FE' : '#F3F4F6'
                        }}
                      >
                        <Text
                          className="text-xs font-medium capitalize"
                          style={{
                            color: event.targetAudience === 'teachers' ? '#2563EB' :
                                   event.targetAudience === 'all' ? '#0284C7' : '#6B7280'
                          }}
                        >
                          {event.targetAudience === 'teachers' ? 'For Teachers' :
                           event.targetAudience === 'all' ? 'For Everyone' : event.targetAudience}
                        </Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Upcoming Events Section */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-base sm:text-lg font-semibold text-gray-800">
              Upcoming Events
            </Text>
            <TouchableOpacity
              onPress={fetchEvents}
              disabled={loading}
              className="p-2"
            >
              <FontAwesome name="refresh" size={16} color={loading ? "#9CA3AF" : "#3B82F6"} />
            </TouchableOpacity>
          </View>

          {(() => {
            // Get today's date in local timezone
            const today = new Date();
            const todayYear = today.getFullYear();
            const todayMonth = today.getMonth() + 1;
            const todayDay = today.getDate();
            const todayDate = new Date(todayYear, todayMonth - 1, todayDay);

            // Filter and sort upcoming events
            const upcomingEvents = events
              .filter(event => {
                try {
                  const [year, month, day] = event.date.split('-').map(Number);
                  const eventDate = new Date(year, month - 1, day);
                  
                  // Only include future events (not today or past)
                  return eventDate > todayDate;
                } catch (e) {
                  console.error('Error processing event date:', e);
                  return false;
                }
              })
              .sort((a, b) => {
                try {
                  const [aYear, aMonth, aDay] = a.date.split('-').map(Number);
                  const [bYear, bMonth, bDay] = b.date.split('-').map(Number);
                  return (
                    new Date(aYear, aMonth - 1, aDay).getTime() - 
                    new Date(bYear, bMonth - 1, bDay).getTime()
                  );
                } catch (e) {
                  return 0;
                }
              });

            console.log(`Found ${upcomingEvents.length} upcoming events`);
            
            if (upcomingEvents.length === 0) {
              return (
                <View className="bg-white rounded-xl p-6 items-center justify-center border border-gray-100">
                  <FontAwesome name="calendar-o" size={32} color="#D1D5DB" />
                  <Text className="text-gray-500 mt-3 text-center text-base">
                    No upcoming events scheduled
                  </Text>
                  <Text className="text-gray-400 text-sm mt-1 text-center">
                    Check back later for new events
                  </Text>
                </View>
              );
            }

            return (
              <View className="space-y-3">
                {upcomingEvents.map((event) => {
                  const [year, month, day] = event.date.split('-').map(Number);
                  const eventDate = new Date(year, month - 1, day);
                  const formattedDate = eventDate.toLocaleDateString('en-IN', {
                    weekday: 'short',
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric'
                  });

                  return (
                    <TouchableOpacity
                      key={`upcoming-${event.id}`}
                      className="bg-white rounded-xl p-4 shadow-sm border border-gray-100"
                      onPress={() => {
                        setSelectedEvent(event);
                        setShowEventModal(true);
                      }}
                      activeOpacity={0.7}
                    >
                      <View className="flex-row justify-between items-start mb-2">
                        <View className="flex-1 pr-2">
                          <Text className="font-medium text-gray-900 text-base">
                            {event.title}
                          </Text>
                          <Text className="text-gray-500 text-sm mt-1">
                            {formattedDate}
                          </Text>
                        </View>
                        <View
                          className="px-2.5 py-1 rounded-full"
                          style={{
                            backgroundColor: `${getEventTypeColor(event.type)}10`,
                            borderWidth: 1,
                            borderColor: getEventTypeColor(event.type)
                          }}
                        >
                          <Text
                            className="text-xs font-medium capitalize"
                            style={{ color: getEventTypeColor(event.type) }}
                          >
                            {event.type}
                          </Text>
                        </View>
                      </View>


                      {event.description && (
                        <Text className="text-gray-600 text-sm mb-3" numberOfLines={2}>
                          {event.description}
                        </Text>
                      )}

                      <View className="flex-row justify-between items-center">
                        <View className="flex-row items-center">
                          <FontAwesome name="calendar" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                          <Text className="text-xs text-gray-500">
                            {formattedDate}
                          </Text>
                        </View>
                        
                        {event.targetAudience && (
                          <View
                            className="px-2 py-0.5 rounded-full"
                            style={{
                              backgroundColor: 
                                event.targetAudience === 'teachers' ? '#EFF6FF' :
                                event.targetAudience === 'all' ? '#F0F9FF' : '#F9FAFB',
                              borderWidth: 1,
                              borderColor: 
                                event.targetAudience === 'teachers' ? '#BFDBFE' :
                                event.targetAudience === 'all' ? '#BAE6FD' : '#E5E7EB'
                            }}
                          >
                            <Text
                              className="text-xs font-medium"
                              style={{
                                color: 
                                  event.targetAudience === 'teachers' ? '#1D4ED8' :
                                  event.targetAudience === 'all' ? '#0369A1' : '#4B5563'
                              }}
                            >
                              {event.targetAudience === 'teachers' ? 'Teachers Only' :
                               event.targetAudience === 'all' ? 'All Staff' : 
                               event.targetAudience.split('_').map(word => 
                                 word.charAt(0).toUpperCase() + word.slice(1)
                               ).join(' ')}
                            </Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            );
          })()}
        </View>
      </ScrollView>

      {/* Event Details Modal */}
      <Modal
        visible={showEventModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowEventModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center">
          <View className="bg-white w-11/12 rounded-xl p-4 shadow-xl">
            {selectedEvent && (
              <>
                <View className="flex-row justify-between items-center mb-4">
                  <Text className="text-xl font-bold text-gray-800">Event Details</Text>
                  <TouchableOpacity onPress={() => setShowEventModal(false)}>
                    <FontAwesome name="close" size={24} color="#6B7280" />
                  </TouchableOpacity>
                </View>

                <View
                  className="p-2 rounded-lg mb-4"
                  style={{ backgroundColor: `${getEventTypeColor(selectedEvent.type)}20` }}
                >
                  <Text
                    className="text-sm font-medium capitalize"
                    style={{ color: getEventTypeColor(selectedEvent.type) }}
                  >
                    {selectedEvent.type}
                  </Text>
                </View>

                <Text className="text-lg font-semibold mb-2">{selectedEvent.title}</Text>

                <View className="space-y-3 mb-4">
                  <View className="flex-row items-center">
                    <FontAwesome name="calendar" size={14} color="#6B7280" style={{ marginRight: 6 }} />
                    <View className="flex-row items-center">
                      {(() => {
                        try {
                          const [year, month, day] = selectedEvent.date.split('-').map(Number);
                          const date = new Date(year, month - 1, day);
                          const today = new Date();
                          const isFutureDate = date > new Date(today.getFullYear(), today.getMonth(), today.getDate());
                          
                          return (
                            <View className="flex-row items-center">
                              <Text>
                                {date.toLocaleDateString('en-IN', {
                                  weekday: 'long',
                                  month: 'long',
                                  day: 'numeric',
                                  year: 'numeric'
                                })}
                              </Text>
                              {isFutureDate && (
                                <Text className="text-orange-500 text-xs ml-2">
                                  (Future Date)
                                </Text>
                              )}
                            </View>
                          );
                        } catch (e) {
                          console.error('Error formatting date:', e);
                          return <Text>Invalid date</Text>;
                        }
                      })()}
                    </View>
                  </View>

                  <View className="flex-row items-center">
                    <FontAwesome name="users" size={14} color="#6B7280" style={{ marginRight: 6 }} />
                    <Text className="text-gray-700">
                      {selectedEvent.targetAudience === 'teachers' ? 'For Teachers' :
                       selectedEvent.targetAudience === 'all' ? 'For Everyone' :
                       selectedEvent.targetAudience === 'specific_class' ? `For Class ${selectedEvent.specificClass || ''}` :
                       selectedEvent.targetAudience}
                    </Text>
                  </View>

                  {selectedEvent.description && (
                    <View className="mt-4">
                      <Text className="text-gray-600">{selectedEvent.description}</Text>
                    </View>
                  )}
                </View>


                <TouchableOpacity
                  className="bg-blue-500 py-3 rounded-lg mt-6"
                  onPress={() => setShowEventModal(false)}
                >
                  <Text className="text-white font-medium text-center">Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}
