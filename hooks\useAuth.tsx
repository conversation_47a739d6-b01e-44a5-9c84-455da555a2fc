import {
    createUserWithEmailAndPassword,
    signOut as firebaseSignOut,
    onAuthStateChanged,
    sendPasswordResetEmail,
    signInWithEmailAndPassword,
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { auth, firestore } from '../config/firebase';

// Types
type UserRole = 'admin' | 'parent' | 'teacher';
type UserStatus = 'pending' | 'approved' | 'rejected';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  status: UserStatus;
  phone?: string; // Added phone field
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, name: string, role: UserRole) => Promise<void>;
  signOut: () => Promise<boolean | void>;
  resetPassword: (email: string) => Promise<void>;
  refreshUserStatus: () => Promise<void>;
}

// Context
const AuthContext = createContext<AuthContextType | null>(null);

// Provider
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Add a check to ensure auth is properly initialized
    if (!auth) {
      console.error('Auth is not initialized');
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const userRef = doc(firestore, 'users', firebaseUser.uid);
          const userSnap = await getDoc(userRef);

          if (userSnap.exists()) {
            const data = userSnap.data();
            setUser({
              id: firebaseUser.uid,
              email: firebaseUser.email!,
              role: data.role,
              name: data.name,
              status: data.status,
              phone: data.phone || '',
            });
          } else {
            // Default user creation
            const defaultData = {
              email: firebaseUser.email,
              role: 'parent',
              name: '',
              status: 'pending',
              createdAt: new Date(),
            };
            await setDoc(userRef, defaultData);

            setUser({
              id: firebaseUser.uid,
              email: firebaseUser.email!,
              role: 'parent',
              name: '',
              status: 'pending',
              phone: '',
            });
          }
        } catch (error) {
          console.error('Failed to fetch user:', error);
          setUser(null);
        }
      } else {
        setUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Login
  const signIn = async (email: string, password: string) => {
    try {
      console.log('Signing in user...');
      // Use a timeout to ensure the operation doesn't hang
      const loginPromise = signInWithEmailAndPassword(auth, email, password);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Login timeout')), 15000)
      );

      // Race between login and timeout
      const result = await Promise.race([loginPromise, timeoutPromise]) as any;
      console.log('Sign in successful');

      // Fetch user data from Firestore
      const userRef = doc(firestore, 'users', result.user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const data = userSnap.data();
        const userData = {
          id: result.user.uid,
          email: result.user.email!,
          role: data.role,
          name: data.name,
          status: data.status,
          phone: data.phone || '',
        };

        // Return user data for immediate use in login component
        return userData;
      }

      return result;
    } catch (error) {
      console.error('Error in signIn:', error);
      throw error;
    }
  };

  // Register
  const signUp = async (email: string, password: string, name: string, role: UserRole) => {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    await setDoc(doc(firestore, 'users', result.user.uid), {
      email,
      name,
      role,
      status: 'pending',
      createdAt: new Date(),
    });
  };

  // Logout
  const signOut = async () => {
    try {
      console.log('Signing out user...');
      await firebaseSignOut(auth);
      // Clear any stored user data
      setUser(null);
      setLoading(false);
      console.log('User signed out successfully');
      return true;
    } catch (error) {
      console.error('Error signing out:', error);
      throw error; // Re-throw to be handled by the caller
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      console.log('Sending password reset email to:', email);
      await sendPasswordResetEmail(auth, email);
      console.log('Password reset email sent successfully');
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error; // Re-throw to be handled by the caller
    }
  };

  // Refresh user status from Firestore
  const refreshUserStatus = async () => {
    try {
      if (!user) return;

      console.log('Refreshing user status for:', user.email);
      const userRef = doc(firestore, 'users', user.id);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const data = userSnap.data();
        // Update the user state with fresh data
        setUser({
          ...user,
          status: data.status, // Update status
          role: data.role,     // Update role
          name: data.name,     // Update name
          phone: data.phone || '',
        });
        console.log('User status refreshed:', data.status);
      }
    } catch (error) {
      console.error('Error refreshing user status:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signUp, signOut, resetPassword, refreshUserStatus }}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used inside an AuthProvider');
  return context;
};
